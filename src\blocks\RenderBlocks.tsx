import React, { Fragment } from 'react'

import { ArchiveBlock } from '@/blocks/ArchiveBlock/Component'
import { CallToActionBlock } from '@/blocks/CallToAction/Component'
import { ContentBlock } from '@/blocks/Content/Component'
import { FormBlock } from '@/blocks/Form/Component'
import { MediaBlock } from '@/blocks/MediaBlock/Component'
import { NPIIntroductionBlock } from '@/blocks/NPIIntroduction/Component'
import { NPIMissionVisionBlock } from '@/blocks/NPIMissionVision/Component'
import { NPIFeaturedProjectsBlock } from '@/blocks/NPIFeaturedPrograms/Component'
import { NPISuccessStoriesBlock } from '@/blocks/NPISuccessStories/Component'
import { NPIStatisticsBlock } from '@/blocks/NPIStatistics/Component'
import { NPIPartnersBlock } from '@/blocks/NPIPartners/Component'
import { NPILatestUpdatesBlock } from '@/blocks/NPILatestUpdates/Component'
import { NPIAboutHeroBlock } from '@/blocks/NPIAboutHero/Component'
import { NPIHistoryTimelineBlock } from '@/blocks/NPIHistoryTimeline/Component'
import { NPIStrategicAlignmentBlock } from '@/blocks/NPIStrategicAlignment/Component'
import { NPIOperationsStructureBlock } from '@/blocks/NPIOperationsStructure/Component'
import { NPIPillarsHeroBlock } from '@/blocks/NPIPillarsHero/Component'
import { NPIStrategicPillarsBlock } from '@/blocks/NPIStrategicPillars/Component'

import { NPIProgramsHeroBlock } from '@/blocks/NPIProgramsHero/Component'
import { NPIProgramsListingBlock } from '@/blocks/NPIProgramsListing/Component'
import { NPIProjectsHeroBlock } from '@/blocks/NPIProjectsHero/Component'
import { NPIProjectsListingBlock } from '@/blocks/NPIProjectsListing/Component'
import { NPIContactFormBlock } from '@/blocks/NPIContactForm/Component'
import { NPISuccessStoriesHeroBlock } from '@/blocks/NPISuccessStoriesHero/Component'
import { NPISuccessStoriesGridBlock } from '@/blocks/NPISuccessStoriesGrid/Component'
import { NPIPartnershipsHeroBlock } from '@/blocks/NPIPartnershipsHero/Component'
import { NPIInvestmentOpportunitiesBlock } from '@/blocks/NPIInvestmentOpportunities/Component'
import { NPIInvestmentOpportunitiesHeroBlock } from '@/blocks/NPIInvestmentOpportunitiesHero/Component'
import { NPIPartnershipModelsBlock } from '@/blocks/NPIPartnershipModels/Component'
import { NPIGetInvolvedHeroBlock } from '@/blocks/NPIGetInvolvedHero/Component'
import { NPIEngagementOpportunitiesBlock } from '@/blocks/NPIEngagementOpportunities/Component'
import { NPIResourcesLibraryBlock } from '@/blocks/NPIResourcesLibrary/Component'
import { NPINewsHeroBlock } from '@/blocks/NPINewsHero/Component'
import { NPINewsListingBlock } from '@/blocks/NPINewsListing/Component'
import { NPIEventsCalendarBlock } from '@/blocks/NPIEventsCalendar/Component'
import { NPIEventsHeroBlock } from '@/blocks/NPIEventsHero/Component'
import { NPIMediaGalleryBlock } from '@/blocks/NPIMediaGallery/Component'
import { NPIMediaGalleryHeroBlock } from '@/blocks/NPIMediaGalleryHero/Component'
import { NPIMediaGalleryContentBlock } from '@/blocks/NPIMediaGalleryContent/Component'
import { NPIOperationsHeroBlock } from '@/blocks/NPIOperationsHero/Component'
import { NPIStrategicAlignmentHeroBlock } from '@/blocks/NPIStrategicAlignmentHero/Component'
import { NPIPartnersHeroBlock } from '@/blocks/NPIPartnersHero/Component'
import { NPIPartnersShowcaseBlock } from '@/blocks/NPIPartnersShowcase/Component'
import { NPIProjectsHeroBlock } from '@/blocks/NPIProjectsHero/Component'
import { NPIProjectsListingBlock } from '@/blocks/NPIProjectsListing/Component'

const blockComponents = {
  archive: ArchiveBlock,
  content: ContentBlock,
  cta: CallToActionBlock,
  formBlock: FormBlock,
  mediaBlock: MediaBlock,
  npiIntroduction: NPIIntroductionBlock,
  npiMissionVision: NPIMissionVisionBlock,
  npiFeaturedProjects: NPIFeaturedProjectsBlock,
  npiSuccessStories: NPISuccessStoriesBlock,
  npiStatistics: NPIStatisticsBlock,
  npiPartners: NPIPartnersBlock,
  npiLatestUpdates: NPILatestUpdatesBlock,
  npiAboutHero: NPIAboutHeroBlock,
  npiHistoryTimeline: NPIHistoryTimelineBlock,
  npiStrategicAlignment: NPIStrategicAlignmentBlock,
  npiOperationsStructure: NPIOperationsStructureBlock,
  npiPillarsHero: NPIPillarsHeroBlock,
  npiStrategicPillars: NPIStrategicPillarsBlock,

  npiProgramsHero: NPIProgramsHeroBlock,
  npiProgramsListing: NPIProgramsListingBlock,
  npiProjectsHero: NPIProjectsHeroBlock,
  npiProjectsListing: NPIProjectsListingBlock,
  npiContactForm: NPIContactFormBlock,
  npiSuccessStoriesHero: NPISuccessStoriesHeroBlock,
  npiSuccessStoriesGrid: NPISuccessStoriesGridBlock,
  npiPartnershipsHero: NPIPartnershipsHeroBlock,
  npiInvestmentOpportunities: NPIInvestmentOpportunitiesBlock,
  npiInvestmentOpportunitiesHero: NPIInvestmentOpportunitiesHeroBlock,
  npiPartnershipModels: NPIPartnershipModelsBlock,
  npiGetInvolvedHero: NPIGetInvolvedHeroBlock,
  npiEngagementOpportunities: NPIEngagementOpportunitiesBlock,
  npiResourcesLibrary: NPIResourcesLibraryBlock,
  npiNewsHero: NPINewsHeroBlock,
  npiNewsListing: NPINewsListingBlock,
  npiEventsCalendar: NPIEventsCalendarBlock,
  npiEventsHero: NPIEventsHeroBlock,
  npiMediaGallery: NPIMediaGalleryBlock,
  npiMediaGalleryHero: NPIMediaGalleryHeroBlock,
  npiMediaGalleryContent: NPIMediaGalleryContentBlock,
  npiOperationsHero: NPIOperationsHeroBlock,
  npiStrategicAlignmentHero: NPIStrategicAlignmentHeroBlock,
  npiPartnersHero: NPIPartnersHeroBlock,
  npiPartnersShowcase: NPIPartnersShowcaseBlock,
  npiProjectsHero: NPIProjectsHeroBlock,
  npiProjectsListing: NPIProjectsListingBlock,
}

export const RenderBlocks: React.FC<{
  blocks: Array<{ blockType: keyof typeof blockComponents; [key: string]: any }>
}> = (props) => {
  const { blocks } = props

  const hasBlocks = blocks && Array.isArray(blocks) && blocks.length > 0

  if (hasBlocks) {
    return (
      <Fragment>
        {blocks.map((block, index) => {
          const { blockType } = block

          if (blockType && blockType in blockComponents) {
            const Block = blockComponents[blockType]

            if (Block) {
              // Add consistent spacing between blocks with proper visual separation
              const isFirstBlock = index === 0
              const isLastBlock = index === blocks.length - 1

              return (
                <section
                  className={`
                    ${isFirstBlock ? 'pt-0' : 'pt-2 lg:pt-4'}
                    ${isLastBlock ? 'pb-0' : 'pb-2 lg:pb-4'}
                    relative
                    min-h-[400px] lg:min-h-[500px]
                    flex items-center
                    ${
                      index % 6 === 0
                        ? 'bg-[#E5E1DC]'
                        : index % 6 === 1
                          ? 'bg-[#8D8F78]/15'
                          : index % 6 === 2
                            ? 'bg-[#CABA9C]/20'
                            : index % 6 === 3
                              ? 'bg-[#4C6444]/12'
                              : index % 6 === 4
                                ? 'bg-[#2F2C29]/8'
                                : 'bg-[#CEC9BC]/25'
                    }
                  `}
                  key={index}
                >
                  <div className="w-full">
                    {/* @ts-expect-error there may be some mismatch between the expected types here */}
                    <Block {...block} disableInnerContainer />
                  </div>
                </section>
              )
            }
          }
          return null
        })}
      </Fragment>
    )
  }

  return null
}
