import React from 'react'
import { NPIHero, NPIHeroTitle, NPIHeroSubtitle, NPIHeroActions } from '@/components/ui/npi-hero'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import { Target, Users, Lightbulb, Database, ArrowDown, FileText } from 'lucide-react'

interface NPIProjectsHeroProps {
  title?: string
  subtitle?: string
  totalProjects?: number
  activeProjects?: number
  beneficiaries?: number
}

export const NPIProjectsHeroBlock: React.FC<NPIProjectsHeroProps> = ({
  title = 'Projects & Initiatives',
  subtitle = 'Comprehensive initiatives driving sustainable development through indigenous knowledge preservation, community empowerment, and natural products innovation across Kenya.',
  totalProjects = 25,
  activeProjects = 18,
  beneficiaries = 5000,
}) => {
  const projectTypes = [
    {
      icon: <Database className="w-6 h-6" />,
      title: 'Knowledge Documentation',
      count: '8 Projects',
      description: 'Preserving traditional wisdom',
    },
    {
      icon: <Lightbulb className="w-6 h-6" />,
      title: 'Innovation & Development',
      count: '6 Projects',
      description: 'Product commercialization',
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: 'Capacity Building',
      count: '7 Projects',
      description: 'Community empowerment',
    },
    {
      icon: <Target className="w-6 h-6" />,
      title: 'IP Protection',
      count: '4 Projects',
      description: 'Rights safeguarding',
    },
  ]

  return (
    <NPIHero variant="gradient" className="min-h-screen pt-20 pb-16">
      <div className="max-w-6xl mx-auto text-center px-4">
        <NPIHeroTitle className="mb-6">{title}</NPIHeroTitle>

        <NPIHeroSubtitle className="mb-10">{subtitle}</NPIHeroSubtitle>

        {/* Key Statistics */}
        <div className="grid md:grid-cols-3 gap-6 mb-10">
          <div className="bg-white/10 backdrop-blur-sm p-6 border border-white/20">
            <div className="text-3xl font-bold text-white mb-2 font-npi">{totalPrograms}</div>
            <div className="text-white/80 font-npi">Total Programs</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm p-6 border border-white/20">
            <div className="text-3xl font-bold text-white mb-2 font-npi">{activePrograms}</div>
            <div className="text-white/80 font-npi">Active Programs</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm p-6 border border-white/20">
            <div className="text-3xl font-bold text-white mb-2 font-npi">
              {beneficiaries.toLocaleString()}+
            </div>
            <div className="text-white/80 font-npi">Beneficiaries</div>
          </div>
        </div>

        {/* Program Types */}
        <div className="grid md:grid-cols-4 gap-4 mb-10">
          {programTypes.map((type, index) => (
            <div
              key={index}
              className="bg-white/10 backdrop-blur-sm p-4 border border-white/20 hover:bg-white/20 transition-all duration-300"
            >
              <div className="bg-white/20 w-10 h-10 flex items-center justify-center mx-auto mb-3 text-white">
                {type.icon}
              </div>
              <h3 className="font-semibold text-white mb-1 text-sm font-npi">{type.title}</h3>
              <div className="text-white/90 text-xs mb-1 font-npi">{type.count}</div>
              <p className="text-white/70 text-xs font-npi">{type.description}</p>
            </div>
          ))}
        </div>

        <NPIHeroActions className="gap-4">
          <NPIButton asChild size="lg" variant="cream">
            <Link href="#programs-listing">
              <ArrowDown className="w-5 h-5 mr-2" />
              Explore Programs
            </Link>
          </NPIButton>

          <NPIButton
            asChild
            size="lg"
            variant="outline"
            className="border-white text-white hover:bg-white hover:text-primary"
          >
            <Link href="/get-involved">
              <Users className="w-5 h-5 mr-2" />
              Get Involved
            </Link>
          </NPIButton>

          <NPIButton asChild size="lg" variant="accent">
            <Link href="/resources/projects-report.pdf">
              <FileText className="w-5 h-5 mr-2" />
              Projects Report
            </Link>
          </NPIButton>
        </NPIHeroActions>
      </div>
    </NPIHero>
  )
}
